"use client";

import { useRef, useState } from "react";
import { createSupabaseBrowserClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";

export default function ChangeBackground() {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const router = useRouter();
  const supabase = createSupabaseBrowserClient();

  async function onPick() {
    inputRef.current?.click();
  }

  async function onChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;
    setIsUploading(true);
    try {
      const { data: auth } = await supabase.auth.getUser();
      const user = auth.user;
      if (!user) throw new Error("Not authenticated");

      const path = `backgrounds/${user.id}/bg.jpg`;
      const { error: uploadErr } = await supabase.storage.from("checkin-photos").upload(path, file, {
        contentType: file.type || "image/jpeg",
        upsert: true,
      });
      if (uploadErr) throw uploadErr;
      const { data: pub } = supabase.storage.from("checkin-photos").getPublicUrl(path);

      // First try to update, if no rows affected, then upsert
      const { data: updateData, error: updateErr } = await supabase
        .from("profiles")
        .update({ background_url: pub.publicUrl })
        .eq("user_id", user.id)
        .select();

      if (updateErr) {
        console.error("Update error:", updateErr);
        throw updateErr;
      }

      // If no rows were updated, the profile doesn't exist, so create it
      if (!updateData || updateData.length === 0) {
        console.log("No profile found, creating new one for user:", user.id);

        // Generate a unique username
        const baseUsername = user.email?.split('@')[0] || 'user';
        const timestamp = Date.now().toString().slice(-4);
        const username = `${baseUsername}_${timestamp}`;

        const { error: insertErr } = await supabase
          .from("profiles")
          .insert({
            user_id: user.id,
            background_url: pub.publicUrl,
            username: username,
            display_name: user.email?.split('@')[0] || 'User'
          });
        if (insertErr) {
          console.error("Insert error:", insertErr);
          throw insertErr;
        }
      }

      router.refresh();
    } catch (e: unknown) {
      const message = e instanceof Error ? e.message : "Failed to update background";
      alert(message);
    } finally {
      setIsUploading(false);
    }
  }

  return (
    <div>
      <input ref={inputRef} type="file" accept="image/*" className="hidden" onChange={onChange} />
      <Button onClick={onPick} disabled={isUploading} variant="secondary">
        {isUploading ? "Uploading..." : "Change Background"}
      </Button>
    </div>
  );
}

